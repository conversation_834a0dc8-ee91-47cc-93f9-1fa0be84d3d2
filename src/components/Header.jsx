const Header = () => {
  return (
    <header id="header" className="header">
      <div className="grid-container epcl-flex">
        <div className="left">
          <nav className="main-nav">
            <ul className="menu">
              <li className="search-button">
                <div className="search-form">
                  <svg className="icon" viewBox="0 0 24 24" width="18" height="18">
                    <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                  </svg>
                  <span className="name">Quick Search...</span>
                </div>
              </li>
            </ul>
          </nav>
        </div>
        
        <div className="center">
          <div className="logo">
            <a href="#">
              <img
                src="https://ghost.estudiopatagon.com/zento-personal/content/images/2024/02/logo-zento-personal-1.svg"
                alt="Zento"
                width="170"
                height="60"
                decoding="async"
              />
            </a>
          </div>
        </div>
        
        <div className="right">
          <a href="#" className="login-button">Login</a>
          <a href="#" className="subscribe-button epcl-button">Subscribe</a>
        </div>
        
        <div className="mobile-menu-button">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path fill="currentColor" d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </div>
      </div>
    </header>
  );
};

export default Header;
